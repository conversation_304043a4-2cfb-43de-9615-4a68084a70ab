import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/wms/warehouse/area/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse/area',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/wms/warehouse/area/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/wms/warehouse/area',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse/area',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/admin/wms/warehouse/area/details',
		method: 'get',
		params: obj,
	});
}

export const list = (params?: Object) => {
	return request({
		url: '/admin/wms/warehouse/area/list',
		method: 'get',
		params,
	});
};