import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/wms/warehouse/location/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse/location',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/wms/warehouse/location/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/wms/warehouse/location',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse/location',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/admin/wms/warehouse/location/details',
		method: 'get',
		params: obj,
	});
} 