import request from '/@/utils/request';

/**
 * 产品信息模型类
 */
export class WmsProductInfo {
	id?: bigint;
	productCategory?: string = '';
	productType?: string = '';
	productCode?: string = '';
	productName?: string = '';
	productFullName?: string = '';
	externalUniqueCode?: string = '';
	mnemonicCode?: string = '';
	specification?: string = '';
	model?: string = '';
	status?: string = '0'; // 默认状态为正常
	brand?: string = '';
	unitPrice?: string = '0';
	baseUnit?: string = '';
	color?: string = '';
	length?: string = '0';
	width?: string = '0';
	height?: string = '0';
	weight?: string = '0';
	minStock?: number | null = null;
	maxStock?: number | null = null;
	safetyStockQuantity?: number | null = null;
	storageTerm?: number | null = null;
	remark?: string = '';
	createBy?: string = '';
	updateBy?: string = '';
	createTime?: string;
	updateTime?: string;
	delFlag?: string; // 默认未删除
}


export const pageList = (params?: Object) => {
	return request({
		url: '/admin/wms/product/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/wms/product',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/wms/product/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/wms/product',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/wms/product',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/admin/wms/product/details',
		method: 'get',
		params: obj,
	});
}

export const list = (params?: Object) => {
	return request({
		url: '/admin/wms/product/list',
		method: 'get',
		params,
	});
};