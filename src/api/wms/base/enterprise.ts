import request from '/@/utils/request';

export class WmsEnterpriseInfo {
	id?: bigint | null = null;
	type?: string = '';
	code?: string = '';
	name?: string = '';
	status?: string = '';
	fullName?: string = '';
	contacts?: string = '';
	mobileTelephoneNumber?: string = '';
	telephoneNumber?: string = '';
	fax?: string = '';
	email?: string = '';
	contactAddress?: string = '';
	remark?: string = '';
	createBy?: string = '';
	updateBy?: string = '';
	createTime?: string;
	updateTime?: string;
	delFlag?: string;
}


export const pageList = (params?: Object) => {
	return request({
		url: '/admin/wms/enterprise/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/wms/enterprise',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/wms/enterprise/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/wms/enterprise',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/wms/enterprise',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/admin/wms/enterprise/details',
		method: 'get',
		params: obj,
	});
}

export const list = (params?: Object) => {
	return request({
		url: '/admin/wms/enterprise/list',
		method: 'get',
		params,
	});
};