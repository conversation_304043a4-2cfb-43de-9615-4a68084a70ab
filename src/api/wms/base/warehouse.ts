import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/wms/warehouse/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/wms/warehouse/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/wms/warehouse',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/wms/warehouse',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/admin/wms/warehouse/details',
		method: 'get',
		params: obj,
	});
}

export const list = (params?: Object) => {
	return request({
		url: '/admin/wms/warehouse/list',
		method: 'get',
		params,
	});
};