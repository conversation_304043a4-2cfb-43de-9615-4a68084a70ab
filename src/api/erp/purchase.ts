import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/erp/purchase/order/page',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/erp/purchase/order',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: String) => {
	return request({
		url: '/admin/erp/purchase/order/' + id,
		method: 'get',
	});
};

export const delObj = (ids: Object) => {
	return request({
		url: '/admin/erp/purchase/order',
		method: 'delete',
		data: ids,
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/erp/purchase/order',
		method: 'put',
		data: obj,
	});
};

export function getDetails(obj: Object) {
	return request({
		url: '/erp/purchase/details',
		method: 'get',
		params: obj,
	});
}

// 获取供应商列表
export const getSupplierList = (params?: Object) => {
	return request({
		url: '/erp/supplier/list',
		method: 'get',
		params,
	});
};

// 获取产品列表
export const getProductList = (params?: Object) => {
	return request({
		url: '/erp/product/list',
		method: 'get',
		params,
	});
};
