<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :model="state.queryForm" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="订单单号" prop="no">
						<el-input v-model="state.queryForm.no" placeholder="请输入订单单号" clearable />
					</el-form-item>
					<el-form-item label="产品" prop="productId">
						<el-select v-model="state.queryForm.productId" clearable filterable placeholder="请选择产品">
							<el-option v-for="item in productList" :key="item.id" :label="item.productName" :value="item.id" />
						</el-select>
					</el-form-item>
					<el-form-item label="供应商" prop="supplierId">
						<el-select v-model="state.queryForm.supplierId" clearable filterable placeholder="请选择供应商">
							<el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id" />
						</el-select>
					</el-form-item>
					<el-form-item label="状态" prop="status">
						<el-select v-model="state.queryForm.status" placeholder="请选择状态" clearable class="!w-240px">
							<el-option v-for="dict in erp_purchase_order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="备注" prop="remark">
						<el-input v-model="state.queryForm.remark" placeholder="请输入备注" clearable />
					</el-form-item>
					<el-form-item label="入库数量" prop="inStatus">
						<el-select v-model="state.queryForm.inStatus" placeholder="请选择入库数量" clearable>
							<el-option label="未入库" value="0" />
							<el-option label="部分入库" value="1" />
							<el-option label="全部入库" value="2" />
						</el-select>
					</el-form-item>
					<el-form-item label="退货数量" prop="returnStatus">
						<el-select v-model="state.queryForm.returnStatus" placeholder="请选择退货数量" clearable>
							<el-option label="未退货" value="0" />
							<el-option label="部分退货" value="1" />
							<el-option label="全部退货" value="2" />
						</el-select>
					</el-form-item>
					<el-form-item label="订单时间" prop="orderTime">
						<el-date-picker
							v-model="state.queryForm.orderTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							type="daterange"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
						/>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'erp_purchase_order_add'">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button icon="delete" type="danger" class="ml10" :disabled="multiple" @click="handleDelete(selectObjs)" v-auth="'erp_purchase_order_del'">
						{{ $t('common.delBtn') }}
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'erp_purchase_export'"
						@exportExcel="exportExcel"
						class="ml10"
						style="float: right; margin-right: 20px"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>

			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="50" />
				<el-table-column min-width="180" label="订单单号" align="center" prop="no" />
				<el-table-column label="产品信息" align="center" prop="productNames" min-width="200" />
				<el-table-column label="供应商" align="center" prop="supplierName" />
				<el-table-column label="订单时间" align="center" prop="orderTime" width="120px" />
				<el-table-column label="创建人" align="center" prop="creatorName" />
				<el-table-column label="总数量" align="center" prop="totalCount" :formatter="erpCountTableColumnFormatter" />
				<el-table-column label="入库数量" align="center" prop="inCount" :formatter="erpCountTableColumnFormatter" />
				<el-table-column label="退货数量" align="center" prop="returnCount" :formatter="erpCountTableColumnFormatter" />
				<el-table-column label="金额合计" align="center" prop="totalProductPrice" :formatter="erpPriceTableColumnFormatter" />
				<el-table-column label="含税金额" align="center" prop="totalPrice" :formatter="erpPriceTableColumnFormatter" />
				<el-table-column label="支付订金" align="center" prop="depositPrice" :formatter="erpPriceTableColumnFormatter" />
				<el-table-column label="状态" align="center" fixed="right" width="90" prop="status">
					<template #default="scope">
						<dict-tag :type="erp_purchase_order_status" :value="scope.row.status" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150" fixed="right">
					<template #default="scope">
						<el-button icon="view" @click="handleView(scope.row)" text type="primary" v-auth="'erp_purchase_view'"> 查看 </el-button>
						<el-button icon="edit-pen" @click="formDialogRef.openDialog(scope.row.id)" text type="primary" v-auth="'erp_purchase_edit'">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete([scope.row.id])" text type="primary" v-auth="'erp_purchase_del'">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<purchase-form @refresh="getDataList()" ref="formDialogRef" />
	</div>
</template>

<script lang="ts" name="erpPurchase" setup>
import { delObj, pageList } from '/@/api/erp/purchase';
import { list as getSupplierList, WmsEnterpriseInfo } from '/@/api/wms/base/enterprise';
import { list as getProductList, WmsProductInfo } from '/@/api/wms/base/product';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { erpCountTableColumnFormatter, erpPriceTableColumnFormatter } from '/@/views/erp/util/erpFormat';

// 引入组件
const PurchaseForm = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const formDialogRef = ref();
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
// 供应商列表
const supplierList = ref<WmsEnterpriseInfo[]>([]);
//  产品列表
const productList = ref<WmsProductInfo[]>([]);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		no: undefined,
		supplierId: undefined,
		productId: undefined,
		orderTime: [],
		status: undefined,
		remark: undefined,
		inStatus: undefined,
		returnStatus: undefined,
	},
	pageList: pageList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const { erp_purchase_order_status } = useDict('erp_purchase_order_status');

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList();
};

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map((val: any) => val.id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中数据，是否继续?');
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch {}
};

// 查看详情
const handleView = (row: any) => {
	formDialogRef.value.openDialog(row.id, 'view');
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/erp/purchase/export', state.queryForm, 'purchase.xlsx');
};

// 获取状态类型
const getStatusType = (status: string) => {
	const statusMap: any = {
		'0': 'warning',
		'1': 'primary',
		'2': 'success',
		'3': 'danger',
	};
	return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
	const statusMap: any = {
		'0': '待审核',
		'1': '已审核',
		'2': '已完成',
		'3': '已取消',
	};
	return statusMap[status] || '未知';
};

// 获取供应商列表
const getSuppliers = async () => {
	try {
		const res = await getSupplierList({ type: '2' });
		supplierList.value = res.data || [];
	} catch (error) {
		console.error('获取供应商列表失败:', error);
	}
};

// 获取供应商列表
const getProducts = async () => {
	try {
		const res = await getProductList();
		productList.value = res.data || [];
	} catch (error) {
		console.error('获取供应商列表失败:', error);
	}
};

// 页面初始化
onMounted(() => {
	getSuppliers();
	getProducts();
});
</script>
