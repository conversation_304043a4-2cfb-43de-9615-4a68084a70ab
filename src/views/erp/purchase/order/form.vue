<template>
	<el-dialog
		:title="form.id ? (mode === 'view' ? '查看采购订单' : '编辑采购订单') : '新增采购订单'"
		v-model="visible"
		width="1200px"
		:close-on-click-modal="false"
		draggable
		:fullscreen="fullscreen"
	>
		<template #header>
			<div class="flex justify-between items-center">
				<span>{{ form.id ? (mode === 'view' ? '查看采购订单' : '编辑采购订单') : '新增采购订单' }}</span>
				<div>
					<el-button icon="full-screen" @click="fullscreen = !fullscreen" text />
				</div>
			</div>
		</template>

		<el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="100px" v-loading="loading" :disabled="mode === 'view'">
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="订单编号" prop="orderNo">
						<el-input v-model="form.orderNo" placeholder="请输入订单编号" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="订单时间" prop="orderTime">
						<el-date-picker v-model="form.orderTime" type="date" placeholder="选择订单时间" style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="供应商" prop="supplierId">
						<el-select v-model="form.supplierId" placeholder="请选择供应商" style="width: 100%">
							<el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="备注" prop="remark">
						<el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" />
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="附件">
						<el-upload
							ref="uploadRef"
							:action="uploadUrl"
							:headers="uploadHeaders"
							:file-list="fileList"
							:on-success="handleUploadSuccess"
							:on-remove="handleRemove"
							:before-upload="beforeUpload"
							multiple
						>
							<el-button type="primary" icon="upload">选择文件</el-button>
						</el-upload>
					</el-form-item>
				</el-col>
			</el-row>

			<!-- 订单产品清单 -->
			<div class="mt20">
				<div class="flex justify-between items-center mb10">
					<h3>订单产品清单</h3>
					<el-button v-if="mode !== 'view'" type="primary" icon="plus" @click="addProduct">添加采购产品</el-button>
				</div>

				<el-table :data="form.productList" border style="width: 100%">
					<el-table-column label="序号" type="index" width="60" />
					<el-table-column label="产品名称" prop="productName" min-width="120">
						<template #default="scope">
							<el-select
								v-if="mode !== 'view'"
								v-model="scope.row.productId"
								placeholder="请选择产品"
								@change="handleProductChange(scope.row, scope.$index)"
								style="width: 100%"
							>
								<el-option v-for="item in productList" :key="item.id" :label="item.productName" :value="item.id" />
							</el-select>
							<span v-else>{{ scope.row.productName }}</span>
						</template>
					</el-table-column>
					<el-table-column label="数量" prop="quantity" width="120">
						<template #default="scope">
							<el-input-number
								v-if="mode !== 'view'"
								v-model="scope.row.quantity"
								:min="1"
								:precision="0"
								@change="calculateRowTotal(scope.row, scope.$index)"
								style="width: 100%"
							/>
							<span v-else>{{ scope.row.quantity }}</span>
						</template>
					</el-table-column>
					<el-table-column label="产品单价" prop="unitPrice" width="120">
						<template #default="scope">
							<el-input-number
								v-if="mode !== 'view'"
								v-model="scope.row.unitPrice"
								:min="0"
								:precision="2"
								@change="calculateRowTotal(scope.row, scope.$index)"
								style="width: 100%"
							/>
							<span v-else>¥{{ scope.row.unitPrice?.toFixed(2) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="金额" prop="amount" width="120">
						<template #default="scope">
							<span>¥{{ scope.row.amount?.toFixed(2) || '0.00' }}</span>
						</template>
					</el-table-column>
					<el-table-column label="税率(%)" prop="taxRate" width="100">
						<template #default="scope">
							<el-input-number
								v-if="mode !== 'view'"
								v-model="scope.row.taxRate"
								:min="0"
								:max="100"
								:precision="2"
								@change="calculateRowTotal(scope.row, scope.$index)"
								style="width: 100%"
							/>
							<span v-else>{{ scope.row.taxRate }}%</span>
						</template>
					</el-table-column>
					<el-table-column label="税额" prop="taxAmount" width="120">
						<template #default="scope">
							<span>¥{{ scope.row.taxAmount?.toFixed(2) || '0.00' }}</span>
						</template>
					</el-table-column>
					<el-table-column label="含税合计" prop="totalAmount" width="120">
						<template #default="scope">
							<span>¥{{ scope.row.totalAmount?.toFixed(2) || '0.00' }}</span>
						</template>
					</el-table-column>
					<el-table-column v-if="mode !== 'view'" label="操作" width="80">
						<template #default="scope">
							<el-button icon="delete" @click="removeProduct(scope.$index)" text type="danger">删除</el-button>
						</template>
					</el-table-column>
				</el-table>

				<!-- 合计信息 -->
				<div class="mt20">
					<el-row :gutter="20">
						<el-col :span="6">
							<div class="total-item">
								<span>合计数量：</span>
								<span class="total-value">{{ totalQuantity }}</span>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="total-item">
								<span>合计金额：</span>
								<span class="total-value">¥{{ totalAmount.toFixed(2) }}</span>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="total-item">
								<span>合计税额：</span>
								<span class="total-value">¥{{ totalTaxAmount.toFixed(2) }}</span>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="total-item">
								<span>含税总计：</span>
								<span class="total-value">¥{{ totalIncludingTax.toFixed(2) }}</span>
							</div>
						</el-col>
					</el-row>
				</div>
			</div>

			<el-row :gutter="20" class="mt20">
				<el-col :span="8">
					<el-form-item label="优惠率(%)" prop="discountRate">
						<el-input-number v-model="form.discountRate" :min="0" :max="100" :precision="2" @change="calculateDiscount" style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="付款优惠" prop="paymentDiscount">
						<el-input-number v-model="form.paymentDiscount" :min="0" :precision="2" style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="优惠后金额" prop="discountedAmount">
						<el-input-number v-model="form.discountedAmount" :precision="2" disabled style="width: 100%" />
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="结算账户" prop="settlementAccount">
						<el-select v-model="form.settlementAccount" placeholder="请选择结算账户" style="width: 100%">
							<el-option label="现金账户" value="cash" />
							<el-option label="银行账户" value="bank" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="支付金额" prop="paymentAmount">
						<el-input-number v-model="form.paymentAmount" :min="0" :precision="2" style="width: 100%" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<template #footer v-if="mode !== 'view'">
			<div class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button type="primary" @click="onSubmit" :loading="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="erpPurchaseForm">
import { addObj, getObj, putObj } from '/@/api/erp/purchase';
import { list as getSupplierList, WmsEnterpriseInfo } from '/@/api/wms/base/enterprise';
import { list as getProductList, WmsProductInfo } from '/@/api/wms/base/product';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { Session } from '/@/utils/storage';

const { t } = useI18n();

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const fullscreen = ref(false);
const mode = ref<string|undefined>('add'); // add, edit, view
const uploadRef = ref();

// 供应商和产品列表
const supplierList = ref<WmsEnterpriseInfo[]>([]);
const productList = ref<WmsProductInfo[]>([]);

// 文件上传相关
const fileList = ref([]);
const uploadUrl = ref(import.meta.env.VITE_API_URL + '/admin/sys-file/upload');
const uploadHeaders = ref({
	Authorization: 'Bearer ' + Session.getToken(),
});

// 表单数据
const form = reactive({
	id: '',
	orderNo: '',
	orderTime: '',
	supplierId: '',
	remark: '',
	discountRate: 0,
	paymentDiscount: 0,
	discountedAmount: 0,
	settlementAccount: '',
	paymentAmount: 0,
	productList: [] as any[],
	attachments: [] as any[],
});

// 表单验证规则
const dataRules = reactive({
	orderNo: [{ required: true, message: '订单编号不能为空', trigger: 'blur' }],
	orderTime: [{ required: true, message: '订单时间不能为空', trigger: 'blur' }],
	supplierId: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
});

// 计算属性 - 总数量
const totalQuantity = computed(() => {
	return form.productList.reduce((sum, item) => sum + (item.quantity || 0), 0);
});

// 计算属性 - 总金额
const totalAmount = computed(() => {
	return form.productList.reduce((sum, item) => sum + (item.amount || 0), 0);
});

// 计算属性 - 总税额
const totalTaxAmount = computed(() => {
	return form.productList.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
});

// 计算属性 - 含税总计
const totalIncludingTax = computed(() => {
	return form.productList.reduce((sum, item) => sum + (item.totalAmount || 0), 0);
});

// 打开弹窗
const openDialog = async (id?: string, dialogMode?: string | 'add') => {
	visible.value = true;
	mode.value = dialogMode;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 加载基础数据
	await getSuppliers();
	await getProducts();

	// 如果是编辑或查看模式，获取数据
	if (id) {
		form.id = id;
		await getPurchaseData(id);
	} else {
		form.productList = [];
		fileList.value = [];
	}
};


// 获取供应商列表
const getSuppliers = async () => {
	try {
		const res = await getSupplierList();
		supplierList.value = res.data || [];
	} catch (error) {
		console.error('获取供应商列表失败:', error);
	}
};

// 获取产品列表
const getProducts = async () => {
	try {
		const res = await getProductList();
		productList.value = res.data || [];
	} catch (error) {
		console.error('获取产品列表失败:', error);
	}
};

// 获取采购订单数据
const getPurchaseData = async (id: string) => {
	try {
		loading.value = true;
		const res = await getObj(id);
		Object.assign(form, res.data);

		// 处理附件列表
		if (res.data.attachments) {
			fileList.value = res.data.attachments.map((item: any) => ({
				name: item.fileName,
				url: item.fileUrl,
				uid: item.id,
			}));
		}
	} catch (error) {
		useMessage().error('获取数据失败');
	} finally {
		loading.value = false;
	}
};

// 添加产品
const addProduct = () => {
	form.productList.push({
		productId: '',
		productName: '',
		quantity: 1,
		unitPrice: 0,
		amount: 0,
		taxRate: 3.0,
		taxAmount: 0,
		totalAmount: 0,
	});
};

// 删除产品
const removeProduct = (index: number) => {
	form.productList.splice(index, 1);
};

// 产品选择变化
const handleProductChange = (row: any, index: number) => {
	const product = productList.value.find((p: any) => p.id === row.productId);
	if (product) {
		row.productName = product.productName;
		row.unitPrice = product.unitPrice || 0;
		calculateRowTotal(row, index);
	}
};

// 计算行总计
const calculateRowTotal = (row: any, index: number) => {
	const quantity = row.quantity || 0;
	const unitPrice = row.unitPrice || 0;
	const taxRate = row.taxRate || 0;

	row.amount = quantity * unitPrice;
	row.taxAmount = row.amount * (taxRate / 100);
	row.totalAmount = row.amount + row.taxAmount;
};

// 计算优惠
const calculateDiscount = () => {
	const discountAmount = totalIncludingTax.value * (form.discountRate / 100);
	form.discountedAmount = totalIncludingTax.value - discountAmount - form.paymentDiscount;
};

// 文件上传成功
const handleUploadSuccess = (response: any, file: any) => {
	if (response.code === 0) {
		form.attachments.push({
			fileName: file.name,
			fileUrl: response.data.url,
			fileId: response.data.id,
		});
		useMessage().success('文件上传成功');
	} else {
		useMessage().error('文件上传失败');
	}
};

// 文件删除
const handleRemove = (file: any) => {
	const index = form.attachments.findIndex((item: any) => item.fileId === file.uid);
	if (index > -1) {
		form.attachments.splice(index, 1);
	}
};

// 文件上传前检查
const beforeUpload = (file: any) => {
	const isLt10M = file.size / 1024 / 1024 < 10;
	if (!isLt10M) {
		useMessage().error('上传文件大小不能超过 10MB!');
	}
	return isLt10M;
};

// 提交表单
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	if (form.productList.length === 0) {
		useMessage().error('请至少添加一个产品');
		return false;
	}

	try {
		loading.value = true;

		// 计算总计信息
		const submitData = {
			...form,
			totalQuantity: totalQuantity.value,
			totalAmount: totalAmount.value,
			totalTaxAmount: totalTaxAmount.value,
			totalIncludingTax: totalIncludingTax.value,
		};

		if (form.id) {
			await putObj(submitData);
			useMessage().success(t('common.editSuccessText'));
		} else {
			await addObj(submitData);
			useMessage().success(t('common.addSuccessText'));
		}

		visible.value = false;
		emit('refresh');
	} catch (error: any) {
		useMessage().error(error.msg || '操作失败');
	} finally {
		loading.value = false;
	}
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.total-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 12px;
	background-color: var(--el-fill-color-light);
	border-radius: 4px;

	.total-value {
		font-weight: bold;
		color: var(--el-color-primary);
	}
}

.mt20 {
	margin-top: 20px;
}

.mb10 {
	margin-bottom: 10px;
}
</style>
