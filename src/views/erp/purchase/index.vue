<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :model="state.queryForm" :inline="true">
					<el-form-item label="订单编号" prop="orderNo">
						<el-input v-model="state.queryForm.orderNo" placeholder="请输入订单编号" clearable style="width: 200px" />
					</el-form-item>
					<el-form-item label="供应商" prop="supplierId">
						<el-select v-model="state.queryForm.supplierId" placeholder="请选择供应商" clearable style="width: 200px">
							<el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id" />
						</el-select>
					</el-form-item>
					<el-form-item label="订单状态" prop="status">
						<el-select v-model="state.queryForm.status" placeholder="请选择状态" clearable style="width: 200px">
							<el-option label="待审核" value="0" />
							<el-option label="已审核" value="1" />
							<el-option label="已完成" value="2" />
							<el-option label="已取消" value="3" />
						</el-select>
					</el-form-item>
					<el-form-item label="订单时间" prop="orderTime">
						<el-date-picker
							v-model="state.queryForm.orderTime"
							type="daterange"
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							style="width: 240px"
						/>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'erp_purchase_add'">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button
						icon="delete"
						type="danger"
						class="ml10"
						:disabled="multiple"
						@click="handleDelete(selectObjs)"
						v-auth="'erp_purchase_del'"
					>
						{{ $t('common.delBtn') }}
					</el-button>
					<el-button plain icon="upload-filled" type="primary" class="ml10" @click="excelUploadRef.show()">
						{{ $t('common.importBtn') }}
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'erp_purchase_export'"
						@exportExcel="exportExcel"
						class="ml10"
						style="float: right; margin-right: 20px"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>

			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="50" />
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="订单编号" prop="orderNo" show-overflow-tooltip min-width="150" />
				<el-table-column label="产品信息" prop="productInfo" show-overflow-tooltip min-width="120" />
				<el-table-column label="供应商" prop="supplierName" show-overflow-tooltip min-width="120" />
				<el-table-column label="订单时间" prop="orderTime" show-overflow-tooltip min-width="120">
					<template #default="scope">
						{{ parseDate(scope.row.orderTime) }}
					</template>
				</el-table-column>
				<el-table-column label="创建人" prop="createBy" show-overflow-tooltip min-width="100" />
				<el-table-column label="总数量" prop="totalQuantity" show-overflow-tooltip min-width="100" />
				<el-table-column label="入库数量" prop="inboundQuantity" show-overflow-tooltip min-width="100" />
				<el-table-column label="最新状态" prop="latestStatus" show-overflow-tooltip min-width="100" />
				<el-table-column label="含税合计" prop="taxIncludedTotal" show-overflow-tooltip min-width="120">
					<template #default="scope">
						¥{{ scope.row.taxIncludedTotal?.toFixed(2) }}
					</template>
				</el-table-column>
				<el-table-column label="含税总价" prop="taxIncludedAmount" show-overflow-tooltip min-width="120">
					<template #default="scope">
						¥{{ scope.row.taxIncludedAmount?.toFixed(2) }}
					</template>
				</el-table-column>
				<el-table-column label="支付状态" prop="paymentStatus" show-overflow-tooltip min-width="100">
					<template #default="scope">
						<el-tag :type="scope.row.paymentStatus === '1' ? 'success' : 'warning'">
							{{ scope.row.paymentStatus === '1' ? '已支付' : '未支付' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="状态" prop="status" show-overflow-tooltip min-width="100">
					<template #default="scope">
						<el-tag :type="getStatusType(scope.row.status)">
							{{ getStatusText(scope.row.status) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150" fixed="right">
					<template #default="scope">
						<el-button icon="view" @click="handleView(scope.row)" text type="primary" v-auth="'erp_purchase_view'">
							查看
						</el-button>
						<el-button icon="edit-pen" @click="formDialogRef.openDialog(scope.row.id)" text type="primary" v-auth="'erp_purchase_edit'">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete([scope.row.id])" text type="primary" v-auth="'erp_purchase_del'">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<purchase-form @refresh="getDataList()" ref="formDialogRef" />
		<!-- 导入excel -->
		<upload-excel
			:title="'导入采购订单'"
			@refreshDataList="getDataList"
			ref="excelUploadRef"
			temp-url="/admin/sys-file/local/file/purchase.xlsx"
			url="/erp/purchase/import"
		/>
	</div>
</template>

<script lang="ts" name="erpPurchase" setup>
import { delObj, pageList, getSupplierList } from '/@/api/erp/purchase';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 引入组件
const PurchaseForm = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const formDialogRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
// 供应商列表
const supplierList = ref([]);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		orderNo: '',
		supplierId: '',
		status: '',
		orderTime: [],
	},
	pageList: pageList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList();
};

// 多选事件
const handleSelectionChange = (objs: any) => {
	selectObjs.value = objs.map((val: any) => val.id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中数据，是否继续?');
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch {}
};

// 查看详情
const handleView = (row: any) => {
	formDialogRef.value.openDialog(row.id, 'view');
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/erp/purchase/export', state.queryForm, 'purchase.xlsx');
};

// 获取状态类型
const getStatusType = (status: string) => {
	const statusMap: any = {
		'0': 'warning',
		'1': 'primary',
		'2': 'success',
		'3': 'danger',
	};
	return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string) => {
	const statusMap: any = {
		'0': '待审核',
		'1': '已审核',
		'2': '已完成',
		'3': '已取消',
	};
	return statusMap[status] || '未知';
};

// 获取供应商列表
const getSuppliers = async () => {
	try {
		const res = await getSupplierList();
		supplierList.value = res.data || [];
	} catch (error) {
		console.error('获取供应商列表失败:', error);
	}
};

// 页面初始化
onMounted(() => {
	getSuppliers();
});
</script>
