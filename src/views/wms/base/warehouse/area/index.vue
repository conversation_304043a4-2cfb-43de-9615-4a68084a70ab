<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<!-- 页面标题 -->
			<div class="page-header-wrapper">
				<el-page-header @back="goBack" :title="warehouseInfo.name ? warehouseInfo.name : $t('area.title')">
					<template #extra>
						<el-button size="small" type="primary" plain icon="Back" @click="goBack">
							{{ $t('area.backToWarehouse') }}
						</el-button>
					</template>
				</el-page-header>
			</div>

			<!-- 搜索表单 -->
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
					<el-form-item :label="$t('area.name')" prop="name">
						<el-input v-model="state.queryForm.name" :placeholder="$t('area.inputNameTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('area.code')" prop="code">
						<el-input v-model="state.queryForm.code" :placeholder="$t('area.inputCodeTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('area.type')" prop="type">
						<el-select v-model="state.queryForm.type" :placeholder="$t('area.selectTypeTip')" clearable>
							<el-option :label="$t('area.typeStorage')" value="1" />
							<el-option :label="$t('area.typePicking')" value="2" />
							<el-option :label="$t('area.typeInspection')" value="3" />
							<el-option :label="$t('area.typeReturn')" value="4" />
							<el-option :label="$t('area.typeShipment')" value="5" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('area.status')" prop="status">
						<el-select v-model="state.queryForm.status" :placeholder="$t('area.selectStatusTip')" clearable>
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in enable_and_disable_status" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<!-- 操作按钮 -->
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button v-auth="'wms_warehouse_area_add'" icon="Plus" type="primary" @click="handleAdd">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_warehouse_area_import'" class="ml10" icon="upload-filled" type="primary" @click="excelUploadRef.show()">
						{{ $t('common.importBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_warehouse_area_del'" :disabled="multiple" class="ml10" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						{{ $t('common.delBtn') }}
					</el-button>

					<span v-if="selectObjs.length > 0" class="ml10 selection-info">
						<el-tag size="small" type="info">已选择: {{ selectObjs.length }}</el-tag>
						<el-button type="primary" link size="small" @click="clearSelection" class="ml5">清空</el-button>
					</span>

					<right-toolbar 
						v-model:showSearch="showSearch" 
						@queryTable="getDataList" 
						class="ml10 mr20"
						style="float: right"
					/>
				</div>
			</el-row>

			<!-- 卡片列表容器 -->
			<div class="area-card-container">
				<el-row v-loading="state.loading">
					<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item, index) in state.dataList" :key="index" class="mb20">
						<el-card 
							class="area-card" 
							shadow="hover"
							:class="{'is-selected': selectedItemIds.includes(item.id)}"
							@click="handleCardClick(item)"
							@dblclick="handleCardDoubleClick(item)"
						>
							<template #header>
								<div class="card-header">
									<span class="area-name">
										{{ item.name }}
										<el-icon class="edit-icon ml5" @click.stop="areaFormRef.openDialog(item.id)" title="编辑"><EditPen /></el-icon>
									</span>
									<el-switch 
										v-model="item.status" 
										@change="changeStatus(item)" 
										active-value="1" 
										inactive-value="0" 
										class="ml10"
										@click.stop
									></el-switch>
								</div>
							</template>
							<div class="card-content">
								<div class="area-info">
									<p><span class="label">{{ $t('area.code') }}:</span> {{ item.code }}</p>
									<p>
										<span class="label">{{ $t('area.type') }}:</span> 
										<el-tag v-if="item.type === '1'" type="primary" size="small">{{ $t('area.typeStorage') }}</el-tag>
										<el-tag v-else-if="item.type === '2'" type="success" size="small">{{ $t('area.typePicking') }}</el-tag>
										<el-tag v-else-if="item.type === '3'" type="info" size="small">{{ $t('area.typeInspection') }}</el-tag>
										<el-tag v-else-if="item.type === '4'" type="warning" size="small">{{ $t('area.typeReturn') }}</el-tag>
										<el-tag v-else-if="item.type === '5'" type="danger" size="small">{{ $t('area.typeShipment') }}</el-tag>
										<el-tag v-else type="info" size="small">{{ $t('area.typeOther') }}</el-tag>
									</p>
									<p><span class="label">{{ $t('area.manager') }}:</span> {{ item.manager }}</p>
									<p>
										<span class="label">{{ $t('area.locationTotal') }}:</span> {{ item.locationTotal }}
										({{ $t('area.locationUsed') }}: {{ item.locationUsed }})
									</p>
									<p><span class="label">{{ $t('area.usageRate') }}:</span> 
										<el-progress :percentage="calculateUsageRate(item)" :color="getProgressColor(calculateUsageRate(item))"></el-progress>
									</p>
								</div>
							</div>
							<!-- 选中标记 -->
							<div class="select-indicator" v-if="selectedItemIds.includes(item.id)">
								<el-icon><Select /></el-icon>
							</div>
						</el-card>
					</el-col>
				</el-row>
				
				<!-- 暂无数据提示 -->
				<div v-if="!state.loading && (!state.dataList || state.dataList.length === 0)" class="empty-container">
					<el-empty :description="$t('common.noData')"></el-empty>
				</div>
			</div>

			<!-- 分页 -->
			<div class="pagination-container">
				<pagination 
					v-bind="state.pagination"
					@current-change="currentChangeHandle"
					@size-change="sizeChangeHandle"
				></pagination>
			</div>
		</div>

		<!-- 引入表单组件 -->
		<warehouse-area-form 
			ref="areaFormRef" 
			:warehouseId="warehouseId"
			@refresh="getDataList(false)" 
		/>

		<!-- 引入导入组件 -->
		<extend-excel
			ref="excelUploadRef"
			:title="$t('area.importAreaTip')"
			:warehouseId="warehouseId"
			temp-url="/admin/sys-file/local/file/warehouseArea.xlsx"
			url="/admin/wms/warehouse-area/import"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script lang="ts" name="warehouseArea" setup>
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { pageList, delObj, putObj } from '/@/api/wms/base/warehouseArea';
import { getObj as getWarehouseObj } from '/@/api/wms/base/warehouse';
import { Select, EditPen, Delete, Search, Location, User, Histogram } from '@element-plus/icons-vue';

// 动态引入组件
const WarehouseAreaForm = defineAsyncComponent(() => import('./form.vue'));
const ExtendExcel = defineAsyncComponent(() => import('./extend_excel.vue'));

const { t } = useI18n();
const { enable_and_disable_status } = useDict('enable_and_disable_status');
const route = useRoute();
const router = useRouter();

// 获取仓库ID
const warehouseId = ref('');
const warehouseInfo = ref({} as any);
const warehouseLoading = ref(false);

// 定义变量内容
const areaFormRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 记录选中的卡片ID
const selectedItemIds = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 添加点击延时控制变量
const clickTimer = ref<number | null>(null);
const CLICK_DELAY = 200; // 双击判定时间（毫秒）

// 获取仓库信息
const fetchWarehouseInfo = async () => {
	warehouseLoading.value = true;
	try {
		const res = await getWarehouseObj(warehouseId.value);
		warehouseInfo.value = res.data;
	} catch (error) {
		console.error('获取仓库信息失败', error);
	} finally {
		warehouseLoading.value = false;
	}
};

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		name: '',
		code: '',
		type: '',
		status: '',
		warehouseId: ''
	},
	pageList: pageList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	state.queryForm.warehouseId = warehouseId.value;
	clearSelection(); // 清空选择
	getDataList();
};

// 计算使用率
const calculateUsageRate = (row: any) => {
	if (!row.locationTotal || row.locationTotal === 0) return 0;
	return Math.round((row.locationUsed / row.locationTotal) * 100);
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
	if (percentage < 50) return '#67C23A';
	if (percentage < 80) return '#E6A23C';
	return '#F56C6C';
};

// 返回仓库列表
const goBack = () => {
	router.push('/wms/base/warehouse/index');
};

// 处理新增
const handleAdd = () => {
	areaFormRef.value.openDialog();
};

// 处理编辑
const handleEdit = (row: any) => {
	areaFormRef.value.openDialog(row.id);
};

// 处理卡片单击事件
const handleCardClick = (item: any) => {
	if (clickTimer.value === null) {
		clickTimer.value = window.setTimeout(() => {
			// 跳转到库位管理页面
			router.push({
				path: '/wms/base/warehouse/location/index',
				query: {
					warehouseId: warehouseId.value,
					areaId: item.id,
					warehouseName: warehouseInfo.value.name,
					areaName: item.name
				}
			});
			clickTimer.value = null;
		}, CLICK_DELAY);
	}
};

// 处理卡片双击事件
const handleCardDoubleClick = (item: any) => {
	if (clickTimer.value !== null) {
		clearTimeout(clickTimer.value);
		clickTimer.value = null;
	}
	toggleCardSelection(item);
};

// 修改原有的toggleCardSelection函数，移除双击事件绑定
const toggleCardSelection = (item: any) => {
	const index = selectedItemIds.value.indexOf(item.id);
	if (index === -1) {
		selectedItemIds.value.push(item.id);
	} else {
		selectedItemIds.value.splice(index, 1);
	}
	selectObjs.value = [...selectedItemIds.value];
	multiple.value = selectObjs.value.length === 0;
};

// 清空选择
const clearSelection = () => {
	selectedItemIds.value = [];
	selectObjs.value = [];
	multiple.value = true;
};

// 修改状态
const changeStatus = async (row: any) => {
	try {
		await putObj(row);
		useMessage().success(t('common.optSuccessText'));
		getDataList();
	} catch (err: any) {
		useMessage().error(err.msg || t('common.opFailText'));
	}
};

// 处理删除
const handleDelete = async (ids: string[]) => {
	if (!ids || ids.length === 0) {
		useMessage().wraning(t('common.selectFirst'));
		return;
	}
	
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		clearSelection(); // 清空选择
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg || t('common.opFailText'));
	}
};

// 钩子函数
onMounted(() => {
	warehouseId.value = route.query.warehouseId as string;

	state.queryForm.warehouseId = warehouseId.value;
	fetchWarehouseInfo();
	getDataList();
});
</script>

<style lang="scss" scoped>
.page-header-wrapper {
	margin-bottom: 20px;
	padding: 12px 16px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
	display: flex;
	justify-content: space-between;
	align-items: center;

	:deep(.el-page-header) {
		width: 100%;
		
		.el-page-header__left {
			margin-right: 10px;
			
			.el-icon {
				font-size: 16px;
				color: var(--el-color-primary);
				font-weight: bold;
			}
		}
		
		.el-page-header__content {
			font-size: 16px;
			font-weight: bold;
			color: var(--el-text-color-primary);
		}
	}
}

.warehouse-info-card {
	margin-bottom: 20px;
	transition: all 0.3s;
	
	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}
	
	.warehouse-info-content {
		display: flex;
		flex-direction: column;
		gap: 10px;
		
		.warehouse-basic-info {
			display: flex;
			align-items: center;
			gap: 10px;
			
			h3 {
				margin: 0;
				font-size: 18px;
			}
		}
		
		.warehouse-details {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin-top: 5px;
			
			.detail-item {
				display: flex;
				align-items: center;
				gap: 5px;
				color: var(--el-text-color-secondary);
				font-size: 14px;
				
				.el-icon {
					color: var(--el-color-primary);
				}
			}
		}
	}
}

.search-card {
	margin-bottom: 20px;
	
	.search-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.el-icon {
			margin-right: 5px;
		}
	}
}

.operations-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	
	.selection-info {
		display: inline-flex;
		align-items: center;
	}
}

.area-card-container {
	height: calc(100vh - 300px);
	overflow-y: auto;
	padding-right: 10px;
	padding-top: 10px;
	position: relative;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}

.area-card {
	margin-right: 20px;
	transition: all 0.3s;
	position: relative;
	cursor: pointer;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	&.is-selected {
		border: 2px solid var(--el-color-primary);
		transform: translateY(-5px);
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	:deep(.el-card__body) {
		padding: 5px 10px 10px 5px;
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.area-name {
			font-size: 16px;
			font-weight: bold;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: flex;
			align-items: center;
			
			.edit-icon {
				font-size: 14px;
				color: var(--el-color-primary);
				cursor: pointer;
				opacity: 0.8;
				transition: all 0.3s;
				
				&:hover {
					opacity: 1;
					transform: scale(1.2);
				}
			}
		}
	}

	.card-content {
		.area-info {
			.label {
				font-weight: bold;
				display: inline-block;
				width: 100px;
			}
			
			p {
				margin: 8px 0;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}

	.select-indicator {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 22px;
		height: 22px;
		background-color: var(--el-color-primary);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		z-index: 2;
	}
}

.selection-info {
	display: inline-flex;
	align-items: center;
	vertical-align: middle;
}

.el-tag {
	margin-right: 5px;
}

.empty-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
}

.ml5 {
	margin-left: 5px;
}

.ml10 {
	margin-left: 10px;
}

.mb8 {
	margin-bottom: 8px;
}

.mb20 {
	margin-bottom: 20px;
}
</style> 