<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<!-- 搜索表单 -->
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
					<el-form-item :label="$t('warehouse.name')" prop="name">
						<el-input v-model="state.queryForm.name" :placeholder="$t('warehouse.inputNameTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('warehouse.code')" prop="code">
						<el-input v-model="state.queryForm.code" :placeholder="$t('warehouse.inputCodeTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('warehouse.status')" prop="status">
						<el-select :placeholder="$t('warehouse.selectStatusTip')" class="w100" clearable v-model="state.queryForm.status">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in enable_and_disable_status" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<!-- 操作按钮 -->
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button v-auth="'wms_warehouse_add'" icon="folder-add" type="primary" @click="warehouseFormRef.openDialog()">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_warehouse_import'" class="ml10" icon="upload-filled" type="primary" @click="excelUploadRef.show()">
						{{ $t('common.importBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_warehouse_del'" :disabled="multiple" class="ml10" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						{{ $t('common.delBtn') }}
					</el-button>

					<span v-if="selectObjs.length > 0" class="ml10 selection-info">
						<el-tag size="small" type="info">已选择: {{ selectObjs.length }}</el-tag>
						<el-button type="primary" link size="small" @click="clearSelection" class="ml5">清空</el-button>
					</span>

					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'wms_warehouse_export'"
						@exportExcel="exportExcel"
						@queryTable="getDataList"
						class="ml10 mr20"
						style="float: right"
					/>
				</div>
			</el-row>

			<!-- 卡片列表容器 -->
			<div class="warehouse-card-container">
				<el-row v-loading="state.loading">
					<el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item, index) in state.dataList" :key="index" class="mb20">
						<el-card 
							class="warehouse-card" 
							shadow="hover"
							:class="{'is-selected': selectedItemIds.includes(item.id)}"
							@click="handleCardClick(item)"
							@dblclick="handleCardDoubleClick(item)"
						>
							<template #header>
								<div class="card-header">
									<span class="warehouse-name">
										{{ item.name }}
										<el-icon class="edit-icon ml5" @click.stop="warehouseFormRef.openDialog(item.id)" title="编辑"><EditPen /></el-icon>
									</span>
									<el-switch 
										v-model="item.status" 
										@change="changeSwitch(item)" 
										active-value="1" 
										inactive-value="0" 
										class="ml10"
										@click.stop
									></el-switch>
								</div>
							</template>
							<div class="card-content">
								<div class="warehouse-info">
									<p><span class="label">{{ $t('warehouse.code') }}:</span> {{ item.code }}</p>
									<p><span class="label">{{ $t('warehouse.area') }}:</span> {{ item.area }} {{ $t('warehouse.square') }}</p>
									<p><span class="label">{{ $t('warehouse.manager') }}:</span> {{ item.manager }}</p>
									<p><span class="label">{{ $t('warehouse.usageRate') }}:</span>
										<el-progress :percentage="item.usageRate || 0" :color="getProgressColor(item.usageRate || 0)"></el-progress>
									</p>
								</div>
							</div>
							<!-- 选中标记 -->
							<div class="select-indicator" v-if="selectedItemIds.includes(item.id)">
								<el-icon><Select /></el-icon>
							</div>
						</el-card>
					</el-col>
				</el-row>
				
				<!-- 暂无数据提示 -->
				<div v-if="!state.loading && (!state.dataList || state.dataList.length === 0)" class="empty-container">
					<el-empty :description="$t('common.noData')"></el-empty>
				</div>
			</div>

			<!-- 分页 -->
			<div class="pagination-container">
				<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"></pagination>
			</div>
		</div>

		<!-- 引入表单组件 -->
		<warehouse-form ref="warehouseFormRef" @refresh="getDataList(false)" />
		
		<!-- 引入导入组件 -->
		<extend-excel
			ref="excelUploadRef"
			:title="$t('warehouse.importWarehouseTip')"
			temp-url="/admin/sys-file/local/file/warehouse.xlsx"
			url="/admin/wms/warehouse/import"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script lang="ts" name="warehouse" setup>
import { delObj, pageList, putObj } from '/@/api/wms/base/warehouse';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { Select, EditPen } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { defineAsyncComponent } from 'vue';

// 动态引入组件
const WarehouseForm = defineAsyncComponent(() => import('./form.vue'));
const ExtendExcel = defineAsyncComponent(() => import('./extend_excel.vue'));

const { t } = useI18n();
const { enable_and_disable_status } = useDict('enable_and_disable_status');
const router = useRouter();

// 定义变量内容
const warehouseFormRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 记录选中的卡片ID
const selectedItemIds = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 添加点击延时控制变量
const clickTimer = ref<number | null>(null);
const CLICK_DELAY = 200; // 双击判定时间（毫秒）

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		name: '',
		code: '',
		status: '',
	},
	pageList: pageList,
});


const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  clearSelection(); // 清空选择
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/wms/warehouse/export', state.queryForm, 'warehouse.xlsx');
};

// 处理卡片单击事件
const handleCardClick = (item: any) => {
	if (clickTimer.value === null) {
		clickTimer.value = window.setTimeout(() => {
			goToAreaManage(item);
			clickTimer.value = null;
		}, CLICK_DELAY);
	}
};

// 处理卡片双击事件
const handleCardDoubleClick = (item: any) => {
	if (clickTimer.value !== null) {
		clearTimeout(clickTimer.value);
		clickTimer.value = null;
	}
	toggleCardSelection(item);
};

// 切换卡片选中状态
const toggleCardSelection = (item: any) => {
	const index = selectedItemIds.value.indexOf(item.id);
	if (index === -1) {
		selectedItemIds.value.push(item.id);
	} else {
		selectedItemIds.value.splice(index, 1);
	}
	selectObjs.value = [...selectedItemIds.value];
	multiple.value = selectObjs.value.length === 0;
};

// 清空选择
const clearSelection = () => {
	selectedItemIds.value = [];
	selectObjs.value = [];
	multiple.value = true;
};

// 多选事件（保留兼容）
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		clearSelection(); // 清空选择
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// 表格内开关（状态）
const changeSwitch = async (row: object) => {
	await putObj(row);
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
	if (percentage < 50) return '#67C23A';
	if (percentage < 80) return '#E6A23C';
	return '#F56C6C';
};

// 当页面变化时清空选择
watch(() => state.pagination!.current, () => {
	clearSelection();
});

// 跳转到区域管理页面
const goToAreaManage = (item: any) => {
	router.push({
		path: '/wms/base/warehouse/area/index',
		query: {
			warehouseId: item.id,
			warehouseName: item.name
		}
	});
};
</script>

<style lang="scss" scoped>
.warehouse-card-container {
	height: calc(100vh - 300px);
	overflow-y: auto;
	padding-right: 10px;
	padding-top: 10px;
	position: relative;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}

.warehouse-card {
	margin-right: 20px;
	transition: all 0.3s;
	position: relative;
	cursor: pointer;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	&.is-selected {
		border: 2px solid var(--el-color-primary);
		transform: translateY(-5px);
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	:deep(.el-card__body) {
		padding: 5px 10px 10px 5px;
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.warehouse-name {
			font-size: 16px;
			font-weight: bold;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: flex;
			align-items: center;
			
			.edit-icon {
				font-size: 14px;
				color: var(--el-color-primary);
				cursor: pointer;
				opacity: 0.8;
				transition: all 0.3s;
				
				&:hover {
					opacity: 1;
					transform: scale(1.2);
				}
			}
		}
	}

	.card-content {
		.warehouse-info {
			.label {
				font-weight: bold;
				display: inline-block;
				width: 100px;
			}
			
			p {
				margin: 8px 0;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}

	.select-indicator {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 22px;
		height: 22px;
		background-color: var(--el-color-primary);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		z-index: 2;
	}
}

.selection-info {
	display: inline-flex;
	align-items: center;
	vertical-align: middle;
}

.empty-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10;
}
</style> 