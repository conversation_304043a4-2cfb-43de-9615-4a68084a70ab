<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<!-- 页面标题 -->
			<div class="page-header-wrapper">
				<el-page-header @back="goBack" :title="(warehouseName && areaName) ? warehouseName + '--' + areaName : $t('location.title')">
					<template #extra>
						<el-button size="small" type="primary" plain icon="Back" @click="goBack">
							{{ $t('location.backToArea') }}
						</el-button>
					</template>
				</el-page-header>
			</div>

			<!-- 搜索表单 -->
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
					<el-form-item :label="$t('location.warehouseName')" prop="warehouseId">
						<el-select :placeholder="$t('location.selectWarehouseTip')" class="w100" clearable v-model="state.queryForm.warehouseId">
							<el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in warehouseList" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('location.areaName')" prop="areaId">
						<el-select :placeholder="$t('location.selectAreaTip')" class="w100" clearable v-model="state.queryForm.areaId">
							<el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in areaList" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('location.status')" prop="status">
						<el-select :placeholder="$t('location.selectStatusTip')" class="w100" clearable v-model="state.queryForm.status">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in enable_and_disable_status" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('location.locationName')" prop="locationName">
						<el-input v-model="state.queryForm.locationName" :placeholder="$t('location.inputLocationNameTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('location.locationCode')" prop="locationCode">
						<el-input v-model="state.queryForm.locationCode" :placeholder="$t('location.inputLocationCodeTip')" clearable />
					</el-form-item>
					<el-form-item>
						<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button v-auth="'wms_warehouse_location_add'" icon="folder-add" type="primary" @click="userDialogRef.openDialog()">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_warehouse_location_import'" class="ml10" icon="upload-filled" type="primary" @click="excelUploadRef.show()">
						{{ $t('common.importBtn') }}
					</el-button>

					<el-button plain v-auth="'wms_warehouse_location_del'" :disabled="multiple" class="ml10" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						{{ $t('common.delBtn') }}
					</el-button>

					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'wms_warehouse_location_export'"
						@exportExcel="exportExcel"
						@queryTable="getDataList"
						class="ml10 mr20"
						style="float: right"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column type="selection" width="40" />
				<el-table-column :label="$t('location.index')" type="index" width="60" fixed="left" />
				<el-table-column :label="$t('location.warehouseName')" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('location.areaName')" prop="areaName" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('location.locationCode')" prop="locationCode" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('location.locationName')" prop="locationName" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('location.status')" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" @change="changeSwitch(scope.row)" active-value="1" inactive-value="0"></el-switch>
					</template>
				</el-table-column>
				<el-table-column :label="$t('location.createTime')" prop="createTime" show-overflow-tooltip width="180"></el-table-column>
				<el-table-column :label="$t('common.action')" width="160" fixed="right">
					<template #default="scope">
						<el-button v-auth="'wms_warehouse_location_edit'" icon="edit-pen" text type="primary" @click="userDialogRef.openDialog(scope.row.id)">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button v-auth="'wms_warehouse_location_del'" icon="delete" text type="primary" @click="handleDelete([scope.row.id])">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"></pagination>
		</div>
		<user-form ref="userDialogRef" @refresh="getDataList(false)" :warehouse-id="state.queryForm.warehouseId" :area-id="state.queryForm.areaId"/>

		<extend_excel
			ref="excelUploadRef"
			:title="$t('location.importLocationTip')"
			temp-url="/admin/sys-file/local/file/location.xlsx"
			url="/admin/wms/location/import"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script lang="ts" name="location" setup>
import { delObj, pageList, putObj } from '/@/api/wms/base/location';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { onMounted, ref, reactive, defineAsyncComponent, watch } from 'vue';
import { list as getWarehouseList } from '/@/api/wms/base/warehouse';
import { list as getAreaList } from '/@/api/wms/base/warehouseArea';
import { useRoute, useRouter } from 'vue-router';

// 动态引入组件
const UserForm = defineAsyncComponent(() => import('./form.vue'));
const extend_excel = defineAsyncComponent(() => import('./extend_excel.vue'));

const { t } = useI18n();

const { enable_and_disable_status } = useDict('enable_and_disable_status');
const route = useRoute();
const router = useRouter();

// 获取仓库ID
const warehouseId = ref('');
const areaId = ref('');
const warehouseName = ref('');
const areaName = ref('');

// 定义变量内容
const userDialogRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 仓库列表
const warehouseList = ref([]);
// 库区列表
const areaList = ref([]);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		areaId: '',
		locationName: '',
		locationCode: '',
		status: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	state.queryForm.warehouseId = '';
	state.queryForm.areaId = '';
	state.queryForm.locationName = '';
	state.queryForm.locationCode = '';
	state.queryForm.status = '';
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/wms/location/export', state.queryForm, 'location.xlsx');
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//表格内开关 (状态)
const changeSwitch = async (row: object) => {
	await putObj(row);
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};

// 获取仓库列表
const getWarehouses = async () => {
	try {
		const res = await getWarehouseList();
		warehouseList.value = res.data;
	} catch (err: any) {
		console.error(err);
	}
};

// 获取库区列表
const getAreas = async () => {
	try {
		const res = await getAreaList();
		areaList.value = res.data;
	} catch (err: any) {
		console.error(err);
	}
};

// 返回仓库区域列表
const goBack = () => {
	// 跳转到库位管理页面
	router.push({
		path: '/wms/base/warehouse/area/index',
		query: {
			warehouseId: warehouseId.value,
			warehouseName: warehouseName.value
		}
	});
};

onMounted(() => {
	warehouseId.value = route.query.warehouseId as string;
	areaId.value = route.query.areaId as string;
	warehouseName.value = route.query.warehouseName as string;
	areaName.value = route.query.areaName as string;

	state.queryForm.warehouseId = warehouseId.value;
	state.queryForm.areaId = areaId.value;
	getWarehouses();
	getAreas();
});
</script>

<style lang="scss" scoped>
.page-header-wrapper {
	margin-bottom: 20px;
	padding: 12px 16px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
	display: flex;
	justify-content: space-between;
	align-items: center;

	:deep(.el-page-header) {
		width: 100%;

		.el-page-header__left {
			margin-right: 10px;

			.el-icon {
				font-size: 16px;
				color: var(--el-color-primary);
				font-weight: bold;
			}
		}

		.el-page-header__content {
			font-size: 16px;
			font-weight: bold;
			color: var(--el-text-color-primary);
		}
	}
}
</style>