<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
					<el-form-item :label="$t('product.productCategory')" prop="productCategory">
						<el-select :placeholder="$t('product.selectProductCategoryTip')" class="w100" clearable v-model="state.queryForm.productCategory">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in product_category" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('product.productType')" prop="productType">
						<el-select :placeholder="$t('product.selectProductTypeTip')" class="w100" clearable v-model="state.queryForm.productType">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in product_type" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('product.status')" prop="status">
						<el-select :placeholder="$t('product.selectStatusTip')" class="w100" clearable v-model="state.queryForm.status">
							<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in enable_and_disable_status" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('product.productName')" prop="productName">
						<el-input v-model="state.queryForm.productName" :placeholder="$t('product.inputProductNameTip')" clearable />
					</el-form-item>
					<el-form-item :label="$t('product.productCode')" prop="productCode">
						<el-input v-model="state.queryForm.productCode" :placeholder="$t('product.inputProductCodeTip')" clearable />
					</el-form-item>
					<el-form-item>
						<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button v-auth="'wms_product_add'" icon="folder-add" type="primary" @click="userDialogRef.openDialog()">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button plain v-auth="'wms_product_import'" class="ml10" icon="upload-filled" type="primary" @click="excelUploadRef.show()">
						{{ $t('common.importBtn') }}
					</el-button>

					<el-button plain v-auth="'wms_product_del'" :disabled="multiple" class="ml10" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						{{ $t('common.delBtn') }}
					</el-button>

					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'wms_product_export'"
						@exportExcel="exportExcel"
						@queryTable="getDataList"
						class="ml10 mr20"
						style="float: right"
					/>
				</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column type="selection" width="40" />
				<el-table-column :label="$t('product.index')" type="index" width="60" fixed="left" />
				<el-table-column :label="$t('product.productCategory')" prop="productCategory" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="product_category" :value="scope.row.productCategory"></dict-tag>
					</template>
				</el-table-column>
				<el-table-column :label="$t('product.productType')" prop="productType" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="product_type" :value="scope.row.productType"></dict-tag>
					</template>
				</el-table-column>
				<el-table-column :label="$t('product.productCode')" prop="productCode" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('product.productName')" prop="productName" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('product.specification')" prop="specification" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('product.model')" prop="model" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('product.status')" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" @change="changeSwitch(scope.row)" active-value="1" inactive-value="0"></el-switch>
					</template>
				</el-table-column>
				<el-table-column :label="$t('product.createTime')" prop="createTime" show-overflow-tooltip width="180"></el-table-column>
				<el-table-column :label="$t('common.action')" width="160" fixed="right">
					<template #default="scope">
						<el-button v-auth="'wms_product_edit'" icon="edit-pen" text type="primary" @click="userDialogRef.openDialog(scope.row.id)">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button v-auth="'wms_product_del'" icon="delete" text type="primary" @click="handleDelete([scope.row.id])">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"></pagination>
		</div>
		<user-form ref="userDialogRef" @refresh="getDataList(false)" />

		<extend_excel
			ref="excelUploadRef"
			:title="$t('product.importProductTip')"
			temp-url="/admin/sys-file/local/file/product.xlsx"
			url="/admin/wms/product/import"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script lang="ts" name="product" setup>
import { delObj, pageList, putObj } from '/@/api/wms/base/product';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 动态引入组件
const UserForm = defineAsyncComponent(() => import('./form.vue'));
const extend_excel = defineAsyncComponent(() => import('./extend_excel.vue'));

const { t } = useI18n();

const { product_category } = useDict('product_category');
const { product_type } = useDict('product_type');
const { enable_and_disable_status } = useDict('enable_and_disable_status');

// 定义变量内容
const userDialogRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	state.queryForm.deptId = '';
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/wms/product/export', state.queryForm, 'product.xlsx');
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//表格内开关 (用户状态)
const changeSwitch = async (row: object) => {
	await putObj(row);
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};
</script>
